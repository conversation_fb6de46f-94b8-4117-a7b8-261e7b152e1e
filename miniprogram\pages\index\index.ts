import { setupSafeArea, setupLoginStatusListener, cleanupLoginStatusListener, checkLoginStatus, handleLoginSuccess } from '../../utils/auth';
import { UserInfo } from '../../utils/cloud';

interface VideoInfo {
  id: number;
  title: string;
  desc: string;
  cover: string;
  duration: string;
  videoUrl?: string;
  orientation?: 'portrait' | 'landscape';
  backupVideoUrl?: string; // 备用视频URL
  hasIssue?: boolean; // 是否存在已知问题
}

Page({
  data: {
    safeAreaTop: 0,
    // 登录状态
    isLoggedIn: false,
    openid: '',
    userInfo: null as UserInfo | null,
    // 登录弹窗显示状态
    showLoginPopup: false,
    // 加载状态
    loading: false,
    videos: [
      {
        id: 1,
        title: '八段锦-体大刘晓蕾版',
        desc: '竖屏播放，动作细节更清晰',
        cover: '/images/video-cover1.jpg',
        duration: '12:15',
        videoUrl: 'cloud://cloud1-5go7er9y38b6f1bd.636c-cloud1-5go7er9y38b6f1bd-1366163334/videos/baduanjin-liuxiaolei.mp4',
        orientation: 'portrait', // 竖屏视频
        // 备用视频URL（如果主视频有问题）
        backupVideoUrl: 'cloud://cloud1-5go7er9y38b6f1bd.636c-cloud1-5go7er9y38b6f1bd-1366163334/videos/baduanjin-gwqnb.mp4',
        hasIssue: true // 标记此视频存在问题
      },
      {
        id: 2,
        title: '广卫青年双人版',
        desc: '双人配合，横屏观看更佳',
        cover: '/images/video-cover2.jpg',
        duration: '12:17',
        videoUrl: 'cloud://cloud1-5go7er9y38b6f1bd.636c-cloud1-5go7er9y38b6f1bd-1366163334/videos/baduanjin-gwqnb.mp4',
        orientation: 'landscape' // 横屏视频
      }
    ] as VideoInfo[]
  },

  // 登录状态变化回调
  loginStatusChangeCallback: null as ((isLoggedIn: boolean, userInfo: UserInfo | null) => void) | null,

  onLoad() {
    setupSafeArea(this);
    setupLoginStatusListener(this);
    checkLoginStatus(this);
  },

  onShow() {
    checkLoginStatus(this);
  },

  onUnload() {
    cleanupLoginStatusListener(this);
  },



  showLoginPopup() {
    this.setData({ showLoginPopup: true });
  },

  hideLoginPopup() {
    this.setData({ showLoginPopup: false });
  },

  onLoginSuccess(e: any) {
    const { userInfo, openid } = e.detail;
    handleLoginSuccess(this, userInfo, openid);
  },

  startPractice(e: any) {
    const id = e.currentTarget.dataset.id;
    if (!this.data.isLoggedIn) {
      this.showLoginPopup();
      return;
    }
    wx.navigateTo({
      url: `/pages/video/video?id=${id}`
    });
  }
})
