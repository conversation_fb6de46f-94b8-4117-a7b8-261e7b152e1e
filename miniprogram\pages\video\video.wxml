<!--video.wxml-->
<view class="container">
  <!-- 胶囊按钮安全区域 -->
  <view class="capsule-safe-area" style="height: {{safeAreaTop}}px;"></view>
  
  <!-- 返回按钮 -->
  <view class="back-btn" style="top: {{menuButtonTop}}px; height: {{menuButtonHeight}}px;" bindtap="goBack">
    <view class="back-icon">‹</view>
  </view>

  <!-- 视频播放器 -->
  <view class="video-container">
    <video
      wx:if="{{videoUrl}}"
      id="myVideo"
      src="{{videoUrl}}"
      controls
      autoplay
      show-center-play-btn="{{false}}"
      show-play-btn="{{true}}"
      enable-progress-gesture="{{true}}"
      bindplay="onVideoPlay"
      bindpause="onVideoPause"
      bindended="onVideoEnded"
      bindtimeupdate="onVideoTimeUpdate"
      bindloadedmetadata="onVideoLoaded"
      bindfullscreenchange="onVideoFullScreenChange"
      bindseeking="onVideoSeeking"
      bindseeked="onVideoSeeked"
      binderror="onVideoError"
      bindwaiting="onVideoWaiting"
      class="video-player">
    </video>

    <!-- 视频加载状态提示 -->
    <view wx:if="{{!videoUrl}}" class="loading-container">
      <view class="loading-text">正在加载视频...</view>
    </view>
  </view>


</view>
